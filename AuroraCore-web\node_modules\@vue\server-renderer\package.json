{"_from": "@vue/server-renderer@3.5.18", "_id": "@vue/server-renderer@3.5.18", "_inBundle": false, "_integrity": "sha512-PvIHLUoWgSbDG7zLHqSqaCoZvHi6NNmfVFOqO+OnwvqMz/tqQr3FuGWS8ufluNddk7ZLBJYMrjcw1c6XzR12mA==", "_location": "/@vue/server-renderer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/server-renderer@3.5.18", "name": "@vue/server-renderer", "escapedName": "@vue%2fserver-renderer", "scope": "@vue", "rawSpec": "3.5.18", "saveSpec": null, "fetchSpec": "3.5.18"}, "_requiredBy": ["/vue"], "_resolved": "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.5.18.tgz", "_shasum": "e9fa267b95b3a1d8cddca762377e5de2ae9122bd", "_spec": "@vue/server-renderer@3.5.18", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web\\node_modules\\vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueServerR<PERSON><PERSON>", "formats": ["esm-bundler", "esm-browser", "cjs"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-ssr": "3.5.18", "@vue/shared": "3.5.18"}, "deprecated": false, "description": "@vue/server-renderer", "exports": {".": {"types": "./dist/server-renderer.d.ts", "node": {"production": "./dist/server-renderer.cjs.prod.js", "development": "./dist/server-renderer.cjs.js", "default": "./index.js"}, "module": "./dist/server-renderer.esm-bundler.js", "import": "./dist/server-renderer.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/server-renderer#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/server-renderer.esm-bundler.js", "name": "@vue/server-renderer", "peerDependencies": {"vue": "3.5.18"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/server-renderer"}, "types": "dist/server-renderer.d.ts", "version": "3.5.18"}