{"_from": "vue@^3.3.11", "_id": "vue@3.5.18", "_inBundle": false, "_integrity": "sha512-7W4Y4ZbMiQ3SEo+m9lnoNpV9xG7QVMLa+/0RFwwiAVkeYoyGXqWE85jabU4pllJNUzqfLShJ5YLptewhCWUgNA==", "_location": "/vue", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "vue@^3.3.11", "name": "vue", "escapedName": "vue", "rawSpec": "^3.3.11", "saveSpec": null, "fetchSpec": "^3.3.11"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vue/-/vue-3.5.18.tgz", "_shasum": "3d622425ad1391a2b0138323211ec784f4415686", "_spec": "vue@^3.3.11", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "<PERSON><PERSON>", "formats": ["esm-bundler", "esm-bundler-runtime", "cjs", "global", "global-runtime", "esm-browser", "esm-browser-runtime"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-dom": "3.5.18", "@vue/compiler-sfc": "3.5.18", "@vue/runtime-dom": "3.5.18", "@vue/server-renderer": "3.5.18", "@vue/shared": "3.5.18"}, "deprecated": false, "description": "The progressive JavaScript framework for building modern web UI.", "exports": {".": {"import": {"types": "./dist/vue.d.mts", "node": "./index.mjs", "default": "./dist/vue.runtime.esm-bundler.js"}, "require": {"types": "./dist/vue.d.ts", "node": {"production": "./dist/vue.cjs.prod.js", "development": "./dist/vue.cjs.js", "default": "./index.js"}, "default": "./index.js"}}, "./server-renderer": {"import": {"types": "./server-renderer/index.d.mts", "default": "./server-renderer/index.mjs"}, "require": {"types": "./server-renderer/index.d.ts", "default": "./server-renderer/index.js"}}, "./compiler-sfc": {"import": {"types": "./compiler-sfc/index.d.mts", "browser": "./compiler-sfc/index.browser.mjs", "default": "./compiler-sfc/index.mjs"}, "require": {"types": "./compiler-sfc/index.d.ts", "browser": "./compiler-sfc/index.browser.js", "default": "./compiler-sfc/index.js"}}, "./jsx-runtime": {"types": "./jsx-runtime/index.d.ts", "import": "./jsx-runtime/index.mjs", "require": "./jsx-runtime/index.js"}, "./jsx-dev-runtime": {"types": "./jsx-runtime/index.d.ts", "import": "./jsx-runtime/index.mjs", "require": "./jsx-runtime/index.js"}, "./jsx": "./jsx.d.ts", "./dist/*": "./dist/*", "./package.json": "./package.json"}, "files": ["index.js", "index.mjs", "dist", "compiler-sfc", "server-renderer", "jsx-runtime", "jsx.d.ts"], "homepage": "https://github.com/vuejs/core/tree/main/packages/vue#readme", "jsdelivr": "dist/vue.global.js", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/vue.runtime.esm-bundler.js", "name": "vue", "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git"}, "types": "dist/vue.d.ts", "unpkg": "dist/vue.global.js", "version": "3.5.18"}