{"_from": "@vue/shared@3.5.18", "_id": "@vue/shared@3.5.18", "_inBundle": false, "_integrity": "sha512-cZy8Dq+uuIXbxCZpuLd2GJdeSO/lIzIspC2WtkqIpje5QyFbvLaI5wZtdUjLHjGZrlVX6GilejatWwVYYRc8tA==", "_location": "/@vue/shared", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/shared@3.5.18", "name": "@vue/shared", "escapedName": "@vue%2fshared", "scope": "@vue", "rawSpec": "3.5.18", "saveSpec": null, "fetchSpec": "3.5.18"}, "_requiredBy": ["/@vue/compiler-core", "/@vue/compiler-dom", "/@vue/compiler-sfc", "/@vue/compiler-ssr", "/@vue/reactivity", "/@vue/runtime-core", "/@vue/runtime-dom", "/@vue/server-renderer", "/vue"], "_resolved": "https://registry.npmjs.org/@vue/shared/-/shared-3.5.18.tgz", "_shasum": "529f24a88d3ed678d50fd5c07455841fbe8ac95e", "_spec": "@vue/shared@3.5.18", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web\\node_modules\\vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"formats": ["esm-bundler", "cjs"]}, "bundleDependencies": false, "deprecated": false, "description": "internal utils shared across @vue packages", "exports": {".": {"types": "./dist/shared.d.ts", "node": {"production": "./dist/shared.cjs.prod.js", "development": "./dist/shared.cjs.js", "default": "./index.js"}, "module": "./dist/shared.esm-bundler.js", "import": "./dist/shared.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/shared#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/shared.esm-bundler.js", "name": "@vue/shared", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/shared"}, "sideEffects": false, "types": "dist/shared.d.ts", "version": "3.5.18"}