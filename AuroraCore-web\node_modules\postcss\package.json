{"_from": "postcss@^8.5.6", "_id": "postcss@8.5.6", "_inBundle": false, "_integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==", "_location": "/postcss", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "postcss@^8.5.6", "name": "postcss", "escapedName": "postcss", "rawSpec": "^8.5.6", "saveSpec": null, "fetchSpec": "^8.5.6"}, "_requiredBy": ["/@vue/compiler-sfc", "/vite"], "_resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "_shasum": "2825006615a619b4f62a9e7426cc120b349a8f3c", "_spec": "postcss@^8.5.6", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web\\node_modules\\@vue\\compiler-sfc", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "browser": {"./lib/terminal-highlight": false, "source-map-js": false, "path": false, "url": false, "fs": false}, "bugs": {"url": "https://github.com/postcss/postcss/issues"}, "bundleDependencies": false, "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "deprecated": false, "description": "Tool for transforming styles with JS plugins", "engines": {"node": "^10 || ^12 || >=14"}, "exports": {".": {"import": "./lib/postcss.mjs", "require": "./lib/postcss.js"}, "./lib/at-rule": "./lib/at-rule.js", "./lib/comment": "./lib/comment.js", "./lib/container": "./lib/container.js", "./lib/css-syntax-error": "./lib/css-syntax-error.js", "./lib/declaration": "./lib/declaration.js", "./lib/fromJSON": "./lib/fromJSON.js", "./lib/input": "./lib/input.js", "./lib/lazy-result": "./lib/lazy-result.js", "./lib/no-work-result": "./lib/no-work-result.js", "./lib/list": "./lib/list.js", "./lib/map-generator": "./lib/map-generator.js", "./lib/node": "./lib/node.js", "./lib/parse": "./lib/parse.js", "./lib/parser": "./lib/parser.js", "./lib/postcss": "./lib/postcss.js", "./lib/previous-map": "./lib/previous-map.js", "./lib/processor": "./lib/processor.js", "./lib/result": "./lib/result.js", "./lib/root": "./lib/root.js", "./lib/rule": "./lib/rule.js", "./lib/stringifier": "./lib/stringifier.js", "./lib/stringify": "./lib/stringify.js", "./lib/symbols": "./lib/symbols.js", "./lib/terminal-highlight": "./lib/terminal-highlight.js", "./lib/tokenize": "./lib/tokenize.js", "./lib/warn-once": "./lib/warn-once.js", "./lib/warning": "./lib/warning.js", "./package.json": "./package.json"}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "homepage": "https://postcss.org/", "keywords": ["css", "postcss", "rework", "preprocessor", "parser", "source map", "transform", "manipulation", "transpiler"], "license": "MIT", "main": "./lib/postcss.js", "name": "postcss", "repository": {"type": "git", "url": "git+https://github.com/postcss/postcss.git"}, "types": "./lib/postcss.d.ts", "version": "8.5.6"}