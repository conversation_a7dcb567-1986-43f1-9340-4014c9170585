{"_from": "@vue/compiler-core@3.5.18", "_id": "@vue/compiler-core@3.5.18", "_inBundle": false, "_integrity": "sha512-3slwjQrrV1TO8MoXgy3aynDQ7lslj5UqDxuHnrzHtpON5CBinhWjJETciPngpin/T3OuW3tXUf86tEurusnztw==", "_location": "/@vue/compiler-core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-core@3.5.18", "name": "@vue/compiler-core", "escapedName": "@vue%2fcompiler-core", "scope": "@vue", "rawSpec": "3.5.18", "saveSpec": null, "fetchSpec": "3.5.18"}, "_requiredBy": ["/@vue/compiler-dom", "/@vue/compiler-sfc"], "_resolved": "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.5.18.tgz", "_shasum": "521a138cdd970d9bfd27e42168d12f77a04b2074", "_spec": "@vue/compiler-core@3.5.18", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web\\node_modules\\@vue\\compiler-dom", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueCompilerCore", "compat": true, "formats": ["esm-bundler", "cjs"]}, "bundleDependencies": false, "dependencies": {"@babel/parser": "^7.28.0", "@vue/shared": "3.5.18", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1"}, "deprecated": false, "description": "@vue/compiler-core", "devDependencies": {"@babel/types": "^7.28.1"}, "exports": {".": {"types": "./dist/compiler-core.d.ts", "node": {"production": "./dist/compiler-core.cjs.prod.js", "development": "./dist/compiler-core.cjs.js", "default": "./index.js"}, "module": "./dist/compiler-core.esm-bundler.js", "import": "./dist/compiler-core.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-core#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/compiler-core.esm-bundler.js", "name": "@vue/compiler-core", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-core"}, "types": "dist/compiler-core.d.ts", "version": "3.5.18"}