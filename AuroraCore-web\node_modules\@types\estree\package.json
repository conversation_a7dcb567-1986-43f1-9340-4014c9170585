{"_from": "@types/estree@1.0.8", "_id": "@types/estree@1.0.8", "_inBundle": false, "_integrity": "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==", "_location": "/@types/estree", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/estree@1.0.8", "name": "@types/estree", "escapedName": "@types%2festree", "scope": "@types", "rawSpec": "1.0.8", "saveSpec": null, "fetchSpec": "1.0.8"}, "_requiredBy": ["/rollup"], "_resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz", "_shasum": "958b91c991b1867ced318bedea0e215ee050726e", "_spec": "@types/estree@1.0.8", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web\\node_modules\\rollup", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/RReverser"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for estree", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "license": "MIT", "main": "", "name": "@types/estree", "nonNpm": true, "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/estree"}, "scripts": {}, "typeScriptVersion": "5.1", "types": "index.d.ts", "typesPublisherContentHash": "7a167b6e4a4d9f6e9a2cb9fd3fc45c885f89cbdeb44b3e5961bb057a45c082fd", "version": "1.0.8"}