{"_from": "esbuild@^0.21.3", "_id": "esbuild@0.21.5", "_inBundle": false, "_integrity": "sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==", "_location": "/esbuild", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "esbuild@^0.21.3", "name": "esbuild", "escapedName": "esbuild", "rawSpec": "^0.21.3", "saveSpec": null, "fetchSpec": "^0.21.3"}, "_requiredBy": ["/vite"], "_resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.21.5.tgz", "_shasum": "9ca301b120922959b766360d8ac830da0d02997d", "_spec": "esbuild@^0.21.3", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web\\node_modules\\vite", "bin": {"esbuild": "bin/esbuild"}, "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "bundleDependencies": false, "dependencies": {"@esbuild/aix-ppc64": "0.21.5", "@esbuild/android-arm": "0.21.5", "@esbuild/android-arm64": "0.21.5", "@esbuild/android-x64": "0.21.5", "@esbuild/darwin-arm64": "0.21.5", "@esbuild/darwin-x64": "0.21.5", "@esbuild/freebsd-arm64": "0.21.5", "@esbuild/freebsd-x64": "0.21.5", "@esbuild/linux-arm": "0.21.5", "@esbuild/linux-arm64": "0.21.5", "@esbuild/linux-ia32": "0.21.5", "@esbuild/linux-loong64": "0.21.5", "@esbuild/linux-mips64el": "0.21.5", "@esbuild/linux-ppc64": "0.21.5", "@esbuild/linux-riscv64": "0.21.5", "@esbuild/linux-s390x": "0.21.5", "@esbuild/linux-x64": "0.21.5", "@esbuild/netbsd-x64": "0.21.5", "@esbuild/openbsd-x64": "0.21.5", "@esbuild/sunos-x64": "0.21.5", "@esbuild/win32-arm64": "0.21.5", "@esbuild/win32-ia32": "0.21.5", "@esbuild/win32-x64": "0.21.5"}, "deprecated": false, "description": "An extremely fast JavaScript and CSS bundler and minifier.", "engines": {"node": ">=12"}, "homepage": "https://github.com/evanw/esbuild#readme", "license": "MIT", "main": "lib/main.js", "name": "esbuild", "optionalDependencies": {"@esbuild/aix-ppc64": "0.21.5", "@esbuild/android-arm": "0.21.5", "@esbuild/android-arm64": "0.21.5", "@esbuild/android-x64": "0.21.5", "@esbuild/darwin-arm64": "0.21.5", "@esbuild/darwin-x64": "0.21.5", "@esbuild/freebsd-arm64": "0.21.5", "@esbuild/freebsd-x64": "0.21.5", "@esbuild/linux-arm": "0.21.5", "@esbuild/linux-arm64": "0.21.5", "@esbuild/linux-ia32": "0.21.5", "@esbuild/linux-loong64": "0.21.5", "@esbuild/linux-mips64el": "0.21.5", "@esbuild/linux-ppc64": "0.21.5", "@esbuild/linux-riscv64": "0.21.5", "@esbuild/linux-s390x": "0.21.5", "@esbuild/linux-x64": "0.21.5", "@esbuild/netbsd-x64": "0.21.5", "@esbuild/openbsd-x64": "0.21.5", "@esbuild/sunos-x64": "0.21.5", "@esbuild/win32-arm64": "0.21.5", "@esbuild/win32-ia32": "0.21.5", "@esbuild/win32-x64": "0.21.5"}, "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "scripts": {"postinstall": "node install.js"}, "types": "lib/main.d.ts", "version": "0.21.5"}