{"_from": "@jridgewell/sourcemap-codec@^1.5.0", "_id": "@jridgewell/sourcemap-codec@1.5.5", "_inBundle": false, "_integrity": "sha512-cYQ9310grqxueWbl+WuIUIaiUaDcj7WOq5fVhEljNVgRfOUhY9fy2zTvfoqWsnebh8Sl70VScFbICvJnLKB0Og==", "_location": "/@jridgewell/sourcemap-codec", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@jridgewell/sourcemap-codec@^1.5.0", "name": "@jridgewell/sourcemap-codec", "escapedName": "@jridgewell%2fsourcemap-codec", "scope": "@jridgewell", "rawSpec": "^1.5.0", "saveSpec": null, "fetchSpec": "^1.5.0"}, "_requiredBy": ["/magic-string"], "_resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.5.tgz", "_shasum": "6912b00d2c631c0d15ce1a7ab57cd657f2a8f8ba", "_spec": "@jridgewell/sourcemap-codec@^1.5.0", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web\\node_modules\\magic-string", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Encode/decode sourcemap mappings", "exports": {".": [{"import": {"types": "./types/sourcemap-codec.d.mts", "default": "./dist/sourcemap-codec.mjs"}, "default": {"types": "./types/sourcemap-codec.d.cts", "default": "./dist/sourcemap-codec.umd.js"}}, "./dist/sourcemap-codec.umd.js"], "./package.json": "./package.json"}, "files": ["dist", "src", "types"], "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/sourcemap-codec", "keywords": ["sourcemap", "vlq"], "license": "MIT", "main": "dist/sourcemap-codec.umd.js", "module": "dist/sourcemap-codec.mjs", "name": "@jridgewell/sourcemap-codec", "repository": {"type": "git", "url": "git+https://github.com/jridgewell/sourcemaps.git", "directory": "packages/sourcemap-codec"}, "scripts": {"benchmark": "run-s build:code benchmark:*", "benchmark:install": "cd benchmark && npm install", "benchmark:only": "node --expose-gc benchmark/index.js", "build": "run-s -n build:code build:types", "build:code": "node ../../esbuild.mjs sourcemap-codec.ts", "build:types": "run-s build:types:force build:types:emit build:types:mts", "build:types:emit": "tsc --project tsconfig.build.json", "build:types:force": "rimraf tsconfig.build.tsbuildinfo", "build:types:mts": "node ../../mts-types.mjs", "clean": "run-s -n clean:code clean:types", "clean:code": "tsc --build --clean tsconfig.build.json", "clean:types": "rimraf dist types", "lint": "run-s -n lint:types lint:format", "lint:format": "npm run test:format -- --write", "lint:types": "npm run test:types -- --fix", "prepublishOnly": "npm run-s -n build test", "test": "run-s -n test:types test:only test:format", "test:format": "prettier --check '{src,test}/**/*.ts'", "test:only": "mocha", "test:types": "eslint '{src,test}/**/*.ts'"}, "types": "types/sourcemap-codec.d.cts", "version": "1.5.5"}