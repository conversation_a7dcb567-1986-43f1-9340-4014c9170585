{"_from": "@vue/compiler-dom@3.5.18", "_id": "@vue/compiler-dom@3.5.18", "_inBundle": false, "_integrity": "sha512-RMbU6NTU70++B1JyVJbNbeFkK+A+Q7y9XKE2EM4NLGm2WFR8x9MbAtWxPPLdm0wUkuZv9trpwfSlL6tjdIa1+A==", "_location": "/@vue/compiler-dom", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-dom@3.5.18", "name": "@vue/compiler-dom", "escapedName": "@vue%2fcompiler-dom", "scope": "@vue", "rawSpec": "3.5.18", "saveSpec": null, "fetchSpec": "3.5.18"}, "_requiredBy": ["/@vue/compiler-sfc", "/@vue/compiler-ssr", "/vue"], "_resolved": "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.5.18.tgz", "_shasum": "e13504492c3061ec5bbe6a2e789f15261d4f03a7", "_spec": "@vue/compiler-dom@3.5.18", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web\\node_modules\\vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueCompilerDOM", "compat": true, "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-core": "3.5.18", "@vue/shared": "3.5.18"}, "deprecated": false, "description": "@vue/compiler-dom", "exports": {".": {"types": "./dist/compiler-dom.d.ts", "node": {"production": "./dist/compiler-dom.cjs.prod.js", "development": "./dist/compiler-dom.cjs.js", "default": "./index.js"}, "module": "./dist/compiler-dom.esm-bundler.js", "import": "./dist/compiler-dom.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-dom#readme", "jsdelivr": "dist/compiler-dom.global.js", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/compiler-dom.esm-bundler.js", "name": "@vue/compiler-dom", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-dom"}, "sideEffects": false, "types": "dist/compiler-dom.d.ts", "unpkg": "dist/compiler-dom.global.js", "version": "3.5.18"}