{"_from": "@vue/compiler-ssr@3.5.18", "_id": "@vue/compiler-ssr@3.5.18", "_inBundle": false, "_integrity": "sha512-xM16Ak7rSWHkM3m22NlmcdIM+K4BMyFARAfV9hYFl+SFuRzrZ3uGMNW05kA5pmeMa0X9X963Kgou7ufdbpOP9g==", "_location": "/@vue/compiler-ssr", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-ssr@3.5.18", "name": "@vue/compiler-ssr", "escapedName": "@vue%2fcompiler-ssr", "scope": "@vue", "rawSpec": "3.5.18", "saveSpec": null, "fetchSpec": "3.5.18"}, "_requiredBy": ["/@vue/compiler-sfc", "/@vue/server-renderer"], "_resolved": "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.5.18.tgz", "_shasum": "aecde0b0bff268a9c9014ba66799307c4a784328", "_spec": "@vue/compiler-ssr@3.5.18", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web\\node_modules\\@vue\\compiler-sfc", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"prod": false, "formats": ["cjs"]}, "bundleDependencies": false, "dependencies": {"@vue/compiler-dom": "3.5.18", "@vue/shared": "3.5.18"}, "deprecated": false, "description": "@vue/compiler-ssr", "files": ["dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-ssr#readme", "keywords": ["vue"], "license": "MIT", "main": "dist/compiler-ssr.cjs.js", "name": "@vue/compiler-ssr", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-ssr"}, "types": "dist/compiler-ssr.d.ts", "version": "3.5.18"}