{"_from": "@rollup/rollup-win32-x64-msvc@4.46.2", "_id": "@rollup/rollup-win32-x64-msvc@4.46.2", "_inBundle": false, "_integrity": "sha512-CvUo2ixeIQGtF6WvuB87XWqPQkoFAFqW+HUo/WzHwuHDvIwZCtjdWXoYCcr06iKGydiqTclC4jU/TNObC/xKZg==", "_location": "/@rollup/rollup-win32-x64-msvc", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@rollup/rollup-win32-x64-msvc@4.46.2", "name": "@rollup/rollup-win32-x64-msvc", "escapedName": "@rollup%2frollup-win32-x64-msvc", "scope": "@rollup", "rawSpec": "4.46.2", "saveSpec": null, "fetchSpec": "4.46.2"}, "_requiredBy": ["/rollup"], "_resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.46.2.tgz", "_shasum": "87079f137b5fdb75da11508419aa998cc8cc3d8b", "_spec": "@rollup/rollup-win32-x64-msvc@4.46.2", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web\\node_modules\\rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "bundleDependencies": false, "cpu": ["x64"], "deprecated": false, "description": "Native bindings for Rollup", "files": ["rollup.win32-x64-msvc.node"], "homepage": "https://rollupjs.org/", "license": "MIT", "main": "./rollup.win32-x64-msvc.node", "name": "@rollup/rollup-win32-x64-msvc", "os": ["win32"], "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "version": "4.46.2"}