{"_from": "@vue/runtime-core@3.5.18", "_id": "@vue/runtime-core@3.5.18", "_inBundle": false, "_integrity": "sha512-DUpHa1HpeOQEt6+3nheUfqVXRog2kivkXHUhoqJiKR33SO4x+a5uNOMkV487WPerQkL0vUuRvq/7JhRgLW3S+w==", "_location": "/@vue/runtime-core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/runtime-core@3.5.18", "name": "@vue/runtime-core", "escapedName": "@vue%2fruntime-core", "scope": "@vue", "rawSpec": "3.5.18", "saveSpec": null, "fetchSpec": "3.5.18"}, "_requiredBy": ["/@vue/runtime-dom"], "_resolved": "https://registry.npmjs.org/@vue/runtime-core/-/runtime-core-3.5.18.tgz", "_shasum": "9e9ae8b9491548b53d0cea2bf25746d27c52e191", "_spec": "@vue/runtime-core@3.5.18", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web\\node_modules\\@vue\\runtime-dom", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueRuntimeCore", "formats": ["esm-bundler", "cjs"]}, "bundleDependencies": false, "dependencies": {"@vue/reactivity": "3.5.18", "@vue/shared": "3.5.18"}, "deprecated": false, "description": "@vue/runtime-core", "exports": {".": {"types": "./dist/runtime-core.d.ts", "node": {"production": "./dist/runtime-core.cjs.prod.js", "development": "./dist/runtime-core.cjs.js", "default": "./index.js"}, "module": "./dist/runtime-core.esm-bundler.js", "import": "./dist/runtime-core.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/runtime-core#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/runtime-core.esm-bundler.js", "name": "@vue/runtime-core", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/runtime-core"}, "sideEffects": false, "types": "dist/runtime-core.d.ts", "version": "3.5.18"}