{"_from": "@vue/reactivity@3.5.18", "_id": "@vue/reactivity@3.5.18", "_inBundle": false, "_integrity": "sha512-x0vPO5Imw+3sChLM5Y+B6G1zPjwdOri9e8V21NnTnlEvkxatHEH5B5KEAJcjuzQ7BsjGrKtfzuQ5eQwXh8HXBg==", "_location": "/@vue/reactivity", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/reactivity@3.5.18", "name": "@vue/reactivity", "escapedName": "@vue%2freactivity", "scope": "@vue", "rawSpec": "3.5.18", "saveSpec": null, "fetchSpec": "3.5.18"}, "_requiredBy": ["/@vue/runtime-core", "/@vue/runtime-dom"], "_resolved": "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.5.18.tgz", "_shasum": "fe32166e3938832c54b4134e60e9b58ca7d9bdb4", "_spec": "@vue/reactivity@3.5.18", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web\\node_modules\\@vue\\runtime-dom", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueReactivity", "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "bundleDependencies": false, "dependencies": {"@vue/shared": "3.5.18"}, "deprecated": false, "description": "@vue/reactivity", "exports": {".": {"types": "./dist/reactivity.d.ts", "node": {"production": "./dist/reactivity.cjs.prod.js", "development": "./dist/reactivity.cjs.js", "default": "./index.js"}, "module": "./dist/reactivity.esm-bundler.js", "import": "./dist/reactivity.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/reactivity#readme", "jsdelivr": "dist/reactivity.global.js", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/reactivity.esm-bundler.js", "name": "@vue/reactivity", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/reactivity"}, "sideEffects": false, "types": "dist/reactivity.d.ts", "unpkg": "dist/reactivity.global.js", "version": "3.5.18"}