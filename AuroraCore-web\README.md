# AuroraCore Web

一个基于 Vue 3 + Vite 的现代化前端项目。

## 项目结构

```
AuroraCore-web/
├── public/
│   └── vite.svg          # Vite logo
├── src/
│   ├── assets/
│   │   └── vue.svg       # Vue logo
│   ├── components/
│   │   └── HelloWorld.vue # 示例组件
│   ├── App.vue           # 根组件
│   ├── main.js           # 应用入口
│   └── style.css         # 全局样式
├── index.html            # HTML 模板
├── package.json          # 项目配置和依赖
├── vite.config.js        # Vite 配置
└── README.md             # 项目说明
```

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 下一代前端构建工具
- **JavaScript ES6+** - 现代 JavaScript 语法

## 安装和运行

### 1. 安装依赖

由于可能存在权限问题，请尝试以下方法之一：

#### 方法一：使用管理员权限
```bash
# 以管理员身份运行命令提示符或 PowerShell
npm install
```

#### 方法二：清理 npm 缓存
```bash
npm cache clean --force
npm install
```

#### 方法三：使用 yarn（推荐）
```bash
# 如果没有安装 yarn，先安装
npm install -g yarn

# 使用 yarn 安装依赖
yarn install
```

### 2. 启动开发服务器

```bash
# 使用 npm
npm run dev

# 或使用 yarn
yarn dev
```

### 3. 构建生产版本

```bash
# 使用 npm
npm run build

# 或使用 yarn
yarn build
```

### 4. 预览生产版本

```bash
# 使用 npm
npm run preview

# 或使用 yarn
yarn preview
```

## 开发说明

- 开发服务器默认运行在 `http://localhost:5173`
- 支持热重载，修改代码后会自动刷新页面
- 使用 Vue 3 的 Composition API 和 `<script setup>` 语法

## 项目特性

- ⚡️ 极速的冷启动
- 🔥 HMR (热模块替换)
- 📦 优化的构建
- 🎯 TypeScript 支持（可选）
- 🔧 丰富的插件生态

## 故障排除

如果遇到 npm 权限问题：

1. 确保以管理员身份运行命令
2. 检查防病毒软件是否阻止了文件操作
3. 尝试使用 yarn 替代 npm
4. 清理 npm 缓存：`npm cache clean --force`

## 下一步

项目已经创建完成，您可以：

1. 安装依赖并启动开发服务器
2. 修改 `src/App.vue` 开始开发
3. 在 `src/components/` 目录下添加新组件
4. 根据需要配置路由、状态管理等功能
