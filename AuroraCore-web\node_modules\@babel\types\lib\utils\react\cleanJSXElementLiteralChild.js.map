{"version": 3, "names": ["_index", "require", "_index2", "cleanJSXElementLiteralChild", "child", "args", "lines", "value", "split", "lastNonEmptyLine", "i", "length", "exec", "str", "line", "isFirstLine", "isLastLine", "isLastNonEmptyLine", "trimmedLine", "replace", "push", "inherits", "stringLiteral"], "sources": ["../../../src/utils/react/cleanJSXElementLiteralChild.ts"], "sourcesContent": ["import { stringLiteral } from \"../../builders/generated/index.ts\";\nimport type * as t from \"../../index.ts\";\nimport { inherits } from \"../../index.ts\";\n\nexport default function cleanJSXElementLiteralChild(\n  child: t.JSXText,\n  args: Array<t.Node>,\n) {\n  const lines = child.value.split(/\\r\\n|\\n|\\r/);\n\n  let lastNonEmptyLine = 0;\n\n  for (let i = 0; i < lines.length; i++) {\n    if (/[^ \\t]/.exec(lines[i])) {\n      lastNonEmptyLine = i;\n    }\n  }\n\n  let str = \"\";\n\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i];\n\n    const isFirstLine = i === 0;\n    const isLastLine = i === lines.length - 1;\n    const isLastNonEmptyLine = i === lastNonEmptyLine;\n\n    // replace rendered whitespace tabs with spaces\n    let trimmedLine = line.replace(/\\t/g, \" \");\n\n    // trim whitespace touching a newline\n    if (!isFirstLine) {\n      trimmedLine = trimmedLine.replace(/^ +/, \"\");\n    }\n\n    // trim whitespace touching an endline\n    if (!isLastLine) {\n      trimmedLine = trimmedLine.replace(/ +$/, \"\");\n    }\n\n    if (trimmedLine) {\n      if (!isLastNonEmptyLine) {\n        trimmedLine += \" \";\n      }\n\n      str += trimmedLine;\n    }\n  }\n\n  if (str) args.push(inherits(stringLiteral(str), child));\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AAEe,SAASE,2BAA2BA,CACjDC,KAAgB,EAChBC,IAAmB,EACnB;EACA,MAAMC,KAAK,GAAGF,KAAK,CAACG,KAAK,CAACC,KAAK,CAAC,YAAY,CAAC;EAE7C,IAAIC,gBAAgB,GAAG,CAAC;EAExB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAI,QAAQ,CAACE,IAAI,CAACN,KAAK,CAACI,CAAC,CAAC,CAAC,EAAE;MAC3BD,gBAAgB,GAAGC,CAAC;IACtB;EACF;EAEA,IAAIG,GAAG,GAAG,EAAE;EAEZ,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAMI,IAAI,GAAGR,KAAK,CAACI,CAAC,CAAC;IAErB,MAAMK,WAAW,GAAGL,CAAC,KAAK,CAAC;IAC3B,MAAMM,UAAU,GAAGN,CAAC,KAAKJ,KAAK,CAACK,MAAM,GAAG,CAAC;IACzC,MAAMM,kBAAkB,GAAGP,CAAC,KAAKD,gBAAgB;IAGjD,IAAIS,WAAW,GAAGJ,IAAI,CAACK,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAG1C,IAAI,CAACJ,WAAW,EAAE;MAChBG,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC9C;IAGA,IAAI,CAACH,UAAU,EAAE;MACfE,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC9C;IAEA,IAAID,WAAW,EAAE;MACf,IAAI,CAACD,kBAAkB,EAAE;QACvBC,WAAW,IAAI,GAAG;MACpB;MAEAL,GAAG,IAAIK,WAAW;IACpB;EACF;EAEA,IAAIL,GAAG,EAAER,IAAI,CAACe,IAAI,CAAC,IAAAC,gBAAQ,EAAC,IAAAC,oBAAa,EAACT,GAAG,CAAC,EAAET,KAAK,CAAC,CAAC;AACzD", "ignoreList": []}