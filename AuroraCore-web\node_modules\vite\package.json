{"//": "READ CONTRIBUTING.md to understand what to put under deps vs. devDeps!", "_from": "vite@^5.0.8", "_id": "vite@5.4.19", "_inBundle": false, "_integrity": "sha512-qO3aKv3HoQC8QKiNSTuUM1l9o/XX3+c+VTgLHbJWHZGeTPVAg2XwazI9UWzoxjIJCGCV2zU60uqMzjeLZuULqA==", "_location": "/vite", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "vite@^5.0.8", "name": "vite", "escapedName": "vite", "rawSpec": "^5.0.8", "saveSpec": null, "fetchSpec": "^5.0.8"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/vite/-/vite-5.4.19.tgz", "_shasum": "20efd060410044b3ed555049418a5e7d1998f959", "_spec": "vite@^5.0.8", "_where": "E:\\code\\动态时钟网站\\AuroraCore-ui\\AuroraCore-web", "author": {"name": "<PERSON>"}, "bin": {"vite": "bin/vite.js"}, "bugs": {"url": "https://github.com/vitejs/vite/issues"}, "bundleDependencies": false, "dependencies": {"esbuild": "^0.21.3", "fsevents": "~2.3.3", "postcss": "^8.4.43", "rollup": "^4.20.0"}, "deprecated": false, "description": "Native-ESM powered web dev build tool", "devDependencies": {"@ampproject/remapping": "^2.3.0", "@babel/parser": "^7.25.6", "@jridgewell/trace-mapping": "^0.3.25", "@polka/compression": "^1.0.0-next.25", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-commonjs": "^26.0.1", "@rollup/plugin-dynamic-import-vars": "^2.1.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "15.2.3", "@rollup/pluginutils": "^5.1.0", "@types/escape-html": "^1.0.4", "@types/pnpapi": "^0.0.5", "artichokie": "^0.2.1", "cac": "^6.7.14", "chokidar": "^3.6.0", "connect": "^3.7.0", "convert-source-map": "^2.0.0", "cors": "^2.8.5", "cross-spawn": "^7.0.3", "debug": "^4.3.6", "dep-types": "link:./src/types", "dotenv": "^16.4.5", "dotenv-expand": "^11.0.6", "es-module-lexer": "^1.5.4", "escape-html": "^1.0.3", "estree-walker": "^3.0.3", "etag": "^1.8.1", "fast-glob": "^3.3.2", "http-proxy": "^1.18.1", "launch-editor-middleware": "^2.9.1", "lightningcss": "^1.26.0", "magic-string": "^0.30.11", "micromatch": "^4.0.8", "mlly": "^1.7.1", "mrmime": "^2.0.0", "open": "^8.4.2", "parse5": "^7.1.2", "pathe": "^1.1.2", "periscopic": "^4.0.2", "picocolors": "^1.0.1", "picomatch": "^2.3.1", "postcss-import": "^16.1.0", "postcss-load-config": "^4.0.2", "postcss-modules": "^6.0.0", "resolve.exports": "^2.0.2", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-esbuild": "^6.1.1", "rollup-plugin-license": "^3.5.2", "sass": "^1.77.8", "sass-embedded": "^1.77.8", "sirv": "^2.0.4", "source-map-support": "^0.5.21", "strip-ansi": "^7.1.0", "strip-literal": "^2.1.0", "tsconfck": "^3.1.4", "tslib": "^2.7.0", "types": "link:./types", "ufo": "^1.5.4", "ws": "^8.18.0"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"import": {"types": "./dist/node/index.d.ts", "default": "./dist/node/index.js"}, "require": {"types": "./index.d.cts", "default": "./index.cjs"}}, "./client": {"types": "./client.d.ts"}, "./runtime": {"types": "./dist/node/runtime.d.ts", "import": "./dist/node/runtime.js"}, "./dist/client/*": "./dist/client/*", "./types/*": {"types": "./types/*"}, "./package.json": "./package.json"}, "files": ["bin", "dist", "client.d.ts", "index.cjs", "index.d.cts", "types"], "funding": "https://github.com/vitejs/vite?sponsor=1", "homepage": "https://vite.dev", "keywords": ["frontend", "framework", "hmr", "dev-server", "build-tool", "vite"], "license": "MIT", "main": "./dist/node/index.js", "name": "vite", "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"less": "*", "sass": "*", "stylus": "*", "terser": "^5.4.0", "sugarss": "*", "@types/node": "^18.0.0 || >=20.0.0", "lightningcss": "^1.21.0", "sass-embedded": "*"}, "peerDependenciesMeta": {"less": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "terser": {"optional": true}, "sugarss": {"optional": true}, "@types/node": {"optional": true}, "lightningcss": {"optional": true}, "sass-embedded": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite.git", "directory": "packages/vite"}, "scripts": {"build": "rimraf dist && run-s build-bundle build-types", "build-bundle": "rollup --config rollup.config.ts --configPlugin esbuild", "build-types": "run-s build-types-temp build-types-roll build-types-check", "build-types-check": "tsc --project tsconfig.check.json", "build-types-roll": "rollup --config rollup.dts.config.ts --configPlugin esbuild && rimraf temp", "build-types-temp": "tsc --emitDeclarationOnly --outDir temp -p src/node", "dev": "tsx scripts/dev.ts", "format": "prettier --write --cache --parser typescript \"src/**/*.ts\"", "lint": "eslint --cache --ext .ts src/**", "typecheck": "tsc --noEmit"}, "type": "module", "types": "./dist/node/index.d.ts", "typesVersions": {"*": {"runtime": ["dist/node/runtime.d.ts"]}}, "version": "5.4.19"}